{
	"log增强ㅤ💊": {
		"prefix": [
			"clog"
		], // 片段缩写
		"body": [
			"console.log('💊 $1 ==> ', $1)"
		],
		"description": "log增强ㅤ💊"
	},
	"requiredㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": "required",
		"body": [
			"required"
		],
		"description": "requiredㅤ💊"
	},
	"formatterㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": "formatter",
		"body": [
			"formatter"
		],
		"description": "formatterㅤ💊"
	},
	"validatorㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": "validator",
		"body": [
			"validator (rule, value) {",
			"            if (!value) {",
			"              return new Error('需要年龄')",
			"            } else if (Number(value) < 18) {",
			"              return new Error('年龄应该超过十八岁')",
			"            }",
			"            return true",
			"          },"
		],
		"description": "validatorㅤ💊"
	},
	"watch监听单个ㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": "watch",
		"body": [
			"watch(",
			"  () => props.tableOptions.data,",
			"   (newVal) => {",
			"  },",
			");"
		],
		"description": "watch监听单个ㅤ💊"
	},
	"watch监听多个ㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": "watch",
		"body": [
			"watch(",
			"  () => [flag, flag2],",
			"  (newValue, oldValue) => {",
			"    const flag = newValue.every((e) => e);",
			"    if (flag) change();",
			"  },",
			");"
		],
		"description": "watch-监听多个ㅤ💊"
	},
	"Promiseㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": [
			"promise",
			"Promise",
			"new P"
		],
		"body": [
			"new Promise((resolve, reject) => {});"
		],
		"description": "Promiseㅤ💊"
	},
	"disabled禁用ㅤ💊": {
		"scope": "javascript,typescript,html",
		"prefix": "dis",
		"body": [
			"disabled"
		],
		"description": "disabled禁用ㅤ💊"
	},
	"foreach循环ㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": ".foreach",
		"body": [
			".forEach((e) => {",
			"    console.log('🎃 e ==> ', e)",
			"})"
		],
		"description": "foreach循环ㅤ💊"
	},
	"function快捷片段ㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": "function",
		"body": [
			"function $1() {",
			"  $2",
			"}"
		],
		"description": "函数快捷片段ㅤ💊"
	},
	"then片段ㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": ".then",
		"body": [
			".then((res) => {",
			"    console.log('⚡ res ==> ', res)",
			"    $1",
			"  })"
		],
		"description": ".then片段ㅤ💊"
	},
	"importㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": "import",
		"body": [
			"import $2 from '$1'"
		],
		"description": "引入模块ㅤ💊"
	},
	"import...ㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": "import...",
		"body": [
			"import {$2} from '$1'"
		],
		"description": "引入模块ㅤ💊"
	},
	"definePropsㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": "defineProps",
		"body": [
			"const { title = '标题', enTitle = 'title' } = defineProps<{",
			"  title?: string",
			"  enTitle?: string",
			"}>()"
		],
		"description": ""
	},
	"defineModelㅤ💊": {
		"scope": "javascript,typescript",
		"prefix": "defineModel",
		"body": [
			"const show = defineModel<boolean>('show')"
		],
		"description": "defineModelㅤ💊"
	},
	"pinia快捷导入ㅤ💊": {
		"prefix": "pinia",
		"scope": "javascript,typescript",
		"body": [
			"import { useClassStore } from \"@/stores/modules/class\"",
			"let { config } = $(storeToRefs(useClassStore()))"
		],
		"description": "pinia快捷导入ㅤ💊"
	},
	"echarts配置片段ㅤ💊": {
		"prefix": "echarts",
		"body": [
			"/** @type EChartsOption */",
			"let echartsOptions = $$ref({",
			"  ",
			"})"
		],
		"description": "echarts配置片段ㅤ💊"
	}
}