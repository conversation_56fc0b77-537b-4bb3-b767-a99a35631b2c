{"mcpServers": {"feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "env": {"MCP_DESKTOP_MODE": "true", "MCP_WEB_HOST": "127.0.0.1", "MCP_WEB_PORT": "8765", "MCP_DEBUG": "false"}, "autoApprove": ["interactive_feedback"], "description": "交互式反馈增强服务 - 提供用户交互和反馈收集功能"}, "context7": {"command": "context7-mcp", "args": [], "description": "Upstash Context7服务 - 提供上下文管理和向量搜索功能，用于语义搜索和知识检索"}, "github-mcp": {"url": "https://gitmcp.io/docs", "description": "GitHub MCP服务 - 提供GitHub仓库操作和管理功能"}, "git-mcp-server": {"command": "git-mcp-server", "args": [], "env": {"MCP_LOG_LEVEL": "info", "GIT_SIGN_COMMITS": "false"}, "description": "Git操作服务 - 提供本地Git仓库管理和版本控制功能"}, "sequential-thinking": {"command": "mcp-server-sequential-thinking", "args": [], "description": "ModelContextProtocol顺序思维服务 - 提供逐步推理和思考链功能，支持复杂问题的分步解决"}, "browser-tools-mcp": {"command": "browser-tools-mcp", "args": [], "description": "AgentDesk浏览器工具MCP服务 - 提供网页自动化、浏览器控制和网页内容提取功能"}, "fetcher": {"command": "fetcher-mcp", "args": [], "description": "数据获取服务 - 提供HTTP请求、网页抓取和数据获取功能"}, "wikipedia-mcp": {"command": "uvx", "args": ["wikipedia-mcp@latest"], "description": "Wikipedia MCP服务 - 提供维基百科信息访问和搜索功能"}, "memory-manager": {"command": "uvx", "args": ["enhanced-mcp-memory"], "env": {"LOG_LEVEL": "INFO", "MAX_MEMORY_ITEMS": "1000", "ENABLE_AUTO_CLEANUP": "true"}, "description": "记忆管理服务 - 提供持久化记忆存储和检索功能，用于保存用户明确要求记住的信息"}, "browser-tools-server": {"command": "browser-tools-server", "args": [], "description": "AgentDesk浏览器工具服务器 - 非MCP服务，作为browser-tools-mcp的配套后端服务"}}}